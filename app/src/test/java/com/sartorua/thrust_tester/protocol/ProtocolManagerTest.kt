package com.sartorua.thrust_tester.protocol

import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.delay
import org.junit.Test
import org.junit.Assert.*

class ProtocolManagerTest {

    @Test
    fun testRequestFormatting() {
        val protocolManager = ProtocolManager()
        var sentData = ""
        
        protocolManager.initialize { data ->
            sentData = data
        }
        
        runBlocking {
            // Test INFO request
            val infoRequest = ProtocolManager.ProtocolRequest(ProtocolManager.Command.INFO)
            
            // Start the request in a separate coroutine since it will timeout
            val job = kotlinx.coroutines.launch {
                protocolManager.sendRequest(infoRequest)
            }
            
            // Give it a moment to send the request
            delay(50)
            
            // Check the sent data format
            assertEquals("REQ_INFO\nREQ_END\n", sentData)
            
            job.cancel()
        }
    }
    
    @Test
    fun testSetPwmRequestFormatting() {
        val protocolManager = ProtocolManager()
        var sentData = ""
        
        protocolManager.initialize { data ->
            sentData = data
        }
        
        runBlocking {
            // Test SET_PWM request with parameter
            val setPwmRequest = ProtocolManager.ProtocolRequest(
                ProtocolManager.Command.SET_PWM, 
                "1500"
            )
            
            // Start the request in a separate coroutine since it will timeout
            val job = kotlinx.coroutines.launch {
                protocolManager.sendRequest(setPwmRequest)
            }
            
            // Give it a moment to send the request
            delay(50)
            
            // Check the sent data format
            assertEquals("REQ_SET_PWM:1500\nREQ_END\n", sentData)
            
            job.cancel()
        }
    }
    
    @Test
    fun testResponseParsing() {
        val protocolManager = ProtocolManager()
        
        protocolManager.initialize { _ -> }
        
        runBlocking {
            // Start a request
            val job = kotlinx.coroutines.launch {
                val response = protocolManager.sendRequest(
                    ProtocolManager.ProtocolRequest(ProtocolManager.Command.INFO)
                )
                
                // This should succeed with the mock response
                assertTrue(response.success)
                assertEquals(ProtocolManager.Command.INFO, response.command)
                assertEquals(listOf("Device Info", "Version 1.0"), response.data)
            }
            
            // Give it a moment to start waiting
            delay(50)
            
            // Simulate device response
            protocolManager.processIncomingData("RESP_INFO\nDevice Info\nVersion 1.0\nRESP_END\n")
            
            job.join()
        }
    }
    
    @Test
    fun testTimeout() {
        val protocolManager = ProtocolManager()
        
        protocolManager.initialize { _ -> }
        
        runBlocking {
            val response = protocolManager.sendRequest(
                ProtocolManager.ProtocolRequest(ProtocolManager.Command.INFO)
            )
            
            // Should timeout and return error
            assertFalse(response.success)
            assertEquals(ProtocolManager.Command.INFO, response.command)
            assertTrue(response.errorMessage?.contains("500ms") == true)
        }
    }
    
    @Test
    fun testMultipleLineResponse() {
        val protocolManager = ProtocolManager()
        
        protocolManager.initialize { _ -> }
        
        runBlocking {
            val job = kotlinx.coroutines.launch {
                val response = protocolManager.sendRequest(
                    ProtocolManager.ProtocolRequest(ProtocolManager.Command.DEBUG_START)
                )
                
                assertTrue(response.success)
                assertEquals(3, response.data.size)
                assertEquals("Debug started", response.data[0])
                assertEquals("Sensor 1: OK", response.data[1])
                assertEquals("Sensor 2: OK", response.data[2])
            }
            
            delay(50)
            
            // Simulate multi-line response
            protocolManager.processIncomingData("RESP_DEBUG_START\n")
            protocolManager.processIncomingData("Debug started\n")
            protocolManager.processIncomingData("Sensor 1: OK\n")
            protocolManager.processIncomingData("Sensor 2: OK\n")
            protocolManager.processIncomingData("RESP_END\n")
            
            job.join()
        }
    }
}
