<resources>
    <string name="app_name">Thrust tester</string>
    <string name="title_test">Test</string>
    <string name="title_connect">Connect</string>

    <!-- Protocol Commands -->
    <string name="protocol_commands_title">Protocol Commands</string>
    <string name="button_req_info">REQ_INFO</string>
    <string name="button_req_stop">REQ_STOP</string>
    <string name="button_req_debug_start">REQ_DEBUG_START</string>
    <string name="button_req_debug_stop">REQ_DEBUG_STOP</string>
    <string name="button_req_set_pwm">REQ_SET_PWM</string>
    <string name="pwm_value_label">PWM Value (500-2500):</string>
    <string name="pwm_value_hint">Enter PWM value</string>
    <string name="protocol_error_not_connected">Device not connected</string>
    <string name="protocol_error_invalid_pwm">PWM value must be between 500 and 2500</string>
    <string name="protocol_response_success">Command executed successfully</string>
    <string name="protocol_response_timeout">Devi<PERSON> did not respond within 500ms</string>
</resources>