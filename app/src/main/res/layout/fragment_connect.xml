<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipToPadding="false"
    tools:context=".ui.connect.ConnectFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">



        <!-- USB Device Selection and Connection Control -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="6dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Title with refresh icon -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="USB Serial Device"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <ImageButton
                        android:id="@+id/button_refresh_devices"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_refresh"
                        android:contentDescription="Refresh devices"
                        android:scaleType="centerInside"
                        android:padding="4dp" />

                </LinearLayout>

                <!-- USB Device Spinner -->
                <Spinner
                    android:id="@+id/spinner_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/spinner_background" />

                <!-- Baud Rate and Connect Button -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <Spinner
                        android:id="@+id/spinner_baud_rate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="12dp"
                        android:background="@drawable/spinner_background" />

                    <Button
                        android:id="@+id/button_connection_toggle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1.5"
                        android:text="Connect"
                        style="@style/CustomButton.Primary.Filled" />

                </LinearLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</ScrollView>
