package com.sartorua.thrust_tester

import android.os.Bundle
import android.util.Log
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import androidx.navigation.ui.setupWithNavController
import com.sartorua.thrust_tester.databinding.ActivityMainBinding
import com.sartorua.thrust_tester.usb.UsbSerialManager
import com.sartorua.thrust_tester.usb.ConnectionState
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var usbSerialManager: UsbSerialManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.d(TAG, "Starting MainActivity onCreate")

            binding = ActivityMainBinding.inflate(layoutInflater)
            setContentView(binding.root)

            // Handle window insets for proper status bar and navigation bar handling
            ViewCompat.setOnApplyWindowInsetsListener(binding.root) { view, windowInsets ->
                val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

                // Apply top inset to the container to avoid status bar overlap
                view.updatePadding(top = insets.top)

                // Apply bottom inset to the navigation view to avoid navigation bar overlap
                binding.navView.updatePadding(bottom = insets.bottom)

                windowInsets
            }

            val navView: BottomNavigationView = binding.navView

            val navController = findNavController(R.id.nav_host_fragment_activity_main)
            navView.setupWithNavController(navController)

            // Initialize USB Serial Manager and observe connection state
            usbSerialManager = UsbSerialManager.getInstance(application)
            observeConnectionState()

            Log.d(TAG, "MainActivity onCreate completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error in MainActivity onCreate", e)
            // Don't crash the app, but log the error
        }
    }

    private fun observeConnectionState() {
        lifecycleScope.launch {
            usbSerialManager.connectionState.collect { state ->
                updateNavigationIcon(state)
            }
        }
    }

    private fun updateNavigationIcon(state: ConnectionState) {
        val menu = binding.navView.menu
        val connectMenuItem = menu.findItem(R.id.navigation_connect)

        val iconRes = when (state) {
            ConnectionState.CONNECTED -> R.drawable.ic_plug_connected
            ConnectionState.CONNECTING -> R.drawable.ic_plug_connected // Use connected icon for connecting state
            ConnectionState.DISCONNECTED -> R.drawable.ic_plug_disconnected
        }

        connectMenuItem?.setIcon(iconRes)
        Log.d(TAG, "Updated navigation icon for state: $state")
    }
}