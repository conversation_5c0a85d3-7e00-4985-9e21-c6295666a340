package com.sartorua.thrust_tester.ui.test

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import kotlinx.coroutines.flow.StateFlow
import com.sartorua.thrust_tester.usb.UsbSerialManager
import com.sartorua.thrust_tester.usb.ConnectionState
import com.sartorua.thrust_tester.usb.UsbSerialDevice

class TestViewModel(application: Application) : AndroidViewModel(application) {

    private val usbSerialManager = UsbSerialManager.getInstance(application)

    // Expose USB manager state flows
    val connectionState: StateFlow<ConnectionState> = usbSerialManager.connectionState
    val receivedData: StateFlow<String> = usbSerialManager.receivedData
    val errorMessage: StateFlow<String?> = usbSerialManager.errorMessage

    fun sendData(data: String) {
        usbSerialManager.sendData(data)
    }

    fun clearReceivedData() {
        usbSerialManager.clearReceivedData()
    }

    fun clearError() {
        usbSerialManager.clearError()
    }
}