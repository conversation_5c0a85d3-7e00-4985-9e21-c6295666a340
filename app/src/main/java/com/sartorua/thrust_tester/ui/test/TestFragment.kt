package com.sartorua.thrust_tester.ui.test

import android.graphics.Typeface
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.sartorua.thrust_tester.R
import com.sartorua.thrust_tester.databinding.FragmentTestBinding
import com.sartorua.thrust_tester.usb.ConnectionState
import com.sartorua.thrust_tester.usb.CommunicationEntry
import kotlinx.coroutines.launch

class TestFragment : Fragment() {

    private var _binding: FragmentTestBinding? = null
    private lateinit var testViewModel: TestViewModel

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        testViewModel = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory.getInstance(requireActivity().application))
            .get(TestViewModel::class.java)

        _binding = FragmentTestBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupClickListeners()
        observeViewModel()

        return root
    }

    private fun setupClickListeners() {
        binding.buttonClearLog.setOnClickListener {
            testViewModel.clearCommunicationLog()
        }

        binding.buttonSendData.setOnClickListener {
            val data = binding.editSendData.text.toString()
            if (data.isNotEmpty()) {
                testViewModel.sendData(data + "\n") // Add newline
                binding.editSendData.text?.clear()
            }
        }
    }

    private fun observeViewModel() {
        // Observe connection state to enable/disable send button
        viewLifecycleOwner.lifecycleScope.launch {
            testViewModel.connectionState.collect { state ->
                binding.buttonSendData.isEnabled = (state == ConnectionState.CONNECTED)
            }
        }

        // Observe communication log entries
        viewLifecycleOwner.lifecycleScope.launch {
            testViewModel.communicationLogEntries.collect { entries ->
                updateCommunicationLog(entries)
            }
        }

        // Observe error messages
        viewLifecycleOwner.lifecycleScope.launch {
            testViewModel.errorMessage.collect { error ->
                error?.let {
                    Toast.makeText(requireContext(), it, Toast.LENGTH_LONG).show()
                    testViewModel.clearError()
                }
            }
        }
    }

    private fun updateCommunicationLog(entries: List<CommunicationEntry>) {
        val logContainer = binding.layoutCommunicationLog
        val noDataText = binding.textNoCommunication

        // Clear existing views except the "no data" text
        logContainer.removeAllViews()

        if (entries.isEmpty()) {
            // Show "no data" message
            logContainer.addView(noDataText)
        } else {
            // Hide "no data" message and add log entries
            val primaryColor = ContextCompat.getColor(requireContext(), R.color.orange_primary)
            val onSurfaceColor = ContextCompat.getColor(requireContext(), R.color.text_primary)

            entries.forEach { entry ->
                val textView = TextView(requireContext()).apply {
                    text = entry.getFormattedEntry()
                    textSize = 12f
                    typeface = Typeface.MONOSPACE

                    // Set color based on direction
                    setTextColor(
                        when (entry.direction) {
                            CommunicationEntry.Direction.TX -> primaryColor
                            CommunicationEntry.Direction.RX -> onSurfaceColor
                        }
                    )

                    // Add some padding
                    setPadding(0, 2, 0, 2)
                }
                logContainer.addView(textView)
            }

            // Auto-scroll to bottom
            binding.scrollCommunicationLog.post {
                binding.scrollCommunicationLog.fullScroll(View.FOCUS_DOWN)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}