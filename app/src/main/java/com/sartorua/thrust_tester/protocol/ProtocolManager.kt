package com.sartorua.thrust_tester.protocol

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Manages the bidirectional protocol communication with the connected device.
 * 
 * Protocol format:
 * Request: REQ_COMMAND_NAME[:params]
 *          REQ_END
 * 
 * Response: RESP_COMMAND_NAME
 *           [response data lines]
 *           RESP_END
 */
class ProtocolManager {
    
    companion object {
        private const val TAG = "ProtocolManager"
        private const val RESPONSE_TIMEOUT_MS = 500L
        private const val REQ_PREFIX = "REQ_"
        private const val REQ_END = "REQ_END"
        private const val RESP_PREFIX = "RESP_"
        private const val RESP_END = "RESP_END"
    }
    
    // Protocol commands
    enum class Command(val commandName: String) {
        INFO("INFO"),
        SET_PWM("SET_PWM"),
        STOP("STOP"),
        DEBUG_START("DEBUG_START"),
        DEBUG_STOP("DEBUG_STOP")
    }
    
    data class ProtocolRequest(
        val command: Command,
        val parameter: String? = null
    )
    
    data class ProtocolResponse(
        val command: Command,
        val data: List<String> = emptyList(),
        val success: Boolean = true,
        val errorMessage: String? = null
    )
    
    // State flows for UI updates
    private val _lastResponse = MutableStateFlow<ProtocolResponse?>(null)
    val lastResponse: StateFlow<ProtocolResponse?> = _lastResponse.asStateFlow()
    
    private val _isWaitingForResponse = MutableStateFlow(false)
    val isWaitingForResponse: StateFlow<Boolean> = _isWaitingForResponse.asStateFlow()
    
    // Internal state
    private var pendingRequest: ProtocolRequest? = null
    private var responseJob: Job? = null
    private val receivedLines = ConcurrentLinkedQueue<String>()
    private var sendDataCallback: ((String) -> Unit)? = null
    
    /**
     * Initialize the protocol manager with a callback to send data
     */
    fun initialize(sendDataCallback: (String) -> Unit) {
        this.sendDataCallback = sendDataCallback
    }
    
    /**
     * Send a protocol request and wait for response
     */
    suspend fun sendRequest(request: ProtocolRequest): ProtocolResponse {
        return withContext(Dispatchers.IO) {
            if (_isWaitingForResponse.value) {
                Log.w(TAG, "Already waiting for response, ignoring new request")
                return@withContext ProtocolResponse(
                    command = request.command,
                    success = false,
                    errorMessage = "Already waiting for response"
                )
            }
            
            pendingRequest = request
            _isWaitingForResponse.value = true
            receivedLines.clear()
            
            try {
                // Send the request
                val requestString = buildRequestString(request)
                Log.d(TAG, "Sending request: $requestString")
                sendDataCallback?.invoke(requestString)
                
                // Wait for response with timeout
                val response = withTimeoutOrNull(RESPONSE_TIMEOUT_MS) {
                    waitForResponse(request.command)
                }
                
                if (response != null) {
                    Log.d(TAG, "Received response for ${request.command}: $response")
                    _lastResponse.value = response
                    return@withContext response
                } else {
                    Log.e(TAG, "Timeout waiting for response to ${request.command}")
                    val timeoutResponse = ProtocolResponse(
                        command = request.command,
                        success = false,
                        errorMessage = "Device did not respond within ${RESPONSE_TIMEOUT_MS}ms"
                    )
                    _lastResponse.value = timeoutResponse
                    return@withContext timeoutResponse
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error sending request ${request.command}", e)
                val errorResponse = ProtocolResponse(
                    command = request.command,
                    success = false,
                    errorMessage = "Error sending request: ${e.message}"
                )
                _lastResponse.value = errorResponse
                return@withContext errorResponse
            } finally {
                _isWaitingForResponse.value = false
                pendingRequest = null
            }
        }
    }
    
    /**
     * Process incoming data from the device
     */
    fun processIncomingData(data: String) {
        // Split data into lines and add to queue
        val lines = data.split("\n", "\r\n", "\r")
        for (line in lines) {
            val trimmedLine = line.trim()
            if (trimmedLine.isNotEmpty()) {
                receivedLines.offer(trimmedLine)
                Log.v(TAG, "Queued line: '$trimmedLine'")
            }
        }
    }
    
    private fun buildRequestString(request: ProtocolRequest): String {
        val commandLine = if (request.parameter != null) {
            "$REQ_PREFIX${request.command.commandName}:${request.parameter}"
        } else {
            "$REQ_PREFIX${request.command.commandName}"
        }
        return "$commandLine\n$REQ_END\n"
    }
    
    private suspend fun waitForResponse(expectedCommand: Command): ProtocolResponse {
        return withContext(Dispatchers.IO) {
            val responseData = mutableListOf<String>()
            var foundResponseStart = false
            var foundResponseEnd = false
            
            while (!foundResponseEnd) {
                // Wait for data to be available
                while (receivedLines.isEmpty()) {
                    delay(10) // Small delay to prevent busy waiting
                }
                
                val line = receivedLines.poll() ?: continue
                Log.v(TAG, "Processing line: '$line'")
                
                when {
                    line.startsWith("$RESP_PREFIX${expectedCommand.commandName}") -> {
                        foundResponseStart = true
                        Log.d(TAG, "Found response start for ${expectedCommand.commandName}")
                    }
                    line == RESP_END -> {
                        if (foundResponseStart) {
                            foundResponseEnd = true
                            Log.d(TAG, "Found response end for ${expectedCommand.commandName}")
                        } else {
                            Log.w(TAG, "Found RESP_END without matching RESP_ start")
                        }
                    }
                    foundResponseStart && !foundResponseEnd -> {
                        responseData.add(line)
                        Log.v(TAG, "Added response data: '$line'")
                    }
                    else -> {
                        Log.v(TAG, "Ignoring line (not part of expected response): '$line'")
                    }
                }
            }
            
            ProtocolResponse(
                command = expectedCommand,
                data = responseData,
                success = true
            )
        }
    }
    
    /**
     * Clear any pending state
     */
    fun reset() {
        responseJob?.cancel()
        _isWaitingForResponse.value = false
        pendingRequest = null
        receivedLines.clear()
        _lastResponse.value = null
    }
}
