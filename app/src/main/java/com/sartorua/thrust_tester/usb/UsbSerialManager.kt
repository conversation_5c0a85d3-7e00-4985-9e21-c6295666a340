package com.sartorua.thrust_tester.usb

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import com.hoho.android.usbserial.driver.UsbSerialDriver
import com.hoho.android.usbserial.driver.UsbSerialPort
import com.hoho.android.usbserial.driver.UsbSerialProber
import kotlinx.coroutines.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.IOException

class UsbSerialManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "UsbSerialManager"
        private const val ACTION_USB_PERMISSION = "com.sartorua.thrust_tester.USB_PERMISSION"
        private const val READ_WAIT_MILLIS = 100  // Reduced from 2000ms to 100ms for better responsiveness
        private const val WRITE_WAIT_MILLIS = 1000  // Reduced from 2000ms to 1000ms

        @Volatile
        private var INSTANCE: UsbSerialManager? = null

        fun getInstance(context: Context): UsbSerialManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UsbSerialManager(context.applicationContext).also { INSTANCE = it }
            }
        }

        // Helper function to convert bytes to hex string for debugging
        private fun bytesToHex(bytes: ByteArray, length: Int): String {
            val hexChars = "0123456789ABCDEF"
            val result = StringBuilder()
            for (i in 0 until length) {
                val byte = bytes[i].toInt() and 0xFF
                result.append(hexChars[byte shr 4])
                result.append(hexChars[byte and 0x0F])
                result.append(" ")
            }
            return result.toString().trim()
        }
    }
    
    private val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    private var usbSerialPort: UsbSerialPort? = null
    private var readJob: Job? = null
    
    // State flows for UI updates
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _availableDevices = MutableStateFlow<List<UsbSerialDevice>>(emptyList())
    val availableDevices: StateFlow<List<UsbSerialDevice>> = _availableDevices.asStateFlow()
    
    private val _receivedData = MutableStateFlow("")
    val receivedData: StateFlow<String> = _receivedData.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // USB permission receiver
    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                ACTION_USB_PERMISSION -> {
                    synchronized(this) {
                        val device: UsbDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            intent.getParcelableExtra(UsbManager.EXTRA_DEVICE, UsbDevice::class.java)
                        } else {
                            @Suppress("DEPRECATION")
                            intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                        }
                        if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                            device?.let { connectToDevice(it) }
                        } else {
                            Log.d(TAG, "Permission denied for device $device")
                            _errorMessage.value = "USB permission denied"
                        }
                    }
                }
                UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                    Log.d(TAG, "USB device attached")
                    refreshDeviceList()
                }
                UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                    Log.d(TAG, "USB device detached")
                    disconnect()
                    refreshDeviceList()
                }
            }
        }
    }
    
    init {
        // Register USB receiver with proper flags for newer Android versions
        val filter = IntentFilter().apply {
            addAction(ACTION_USB_PERMISSION)
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        }

        try {
            // Use ContextCompat to handle receiver flags properly across all Android versions
            ContextCompat.registerReceiver(
                context,
                usbReceiver,
                filter,
                ContextCompat.RECEIVER_NOT_EXPORTED
            )
            refreshDeviceList()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register USB receiver", e)
        }
    }
    
    fun refreshDeviceList() {
        try {
            // Check if USB host is supported
            if (!context.packageManager.hasSystemFeature("android.hardware.usb.host")) {
                Log.w(TAG, "USB host not supported on this device")
                _errorMessage.value = "USB host not supported on this device"
                _availableDevices.value = emptyList()
                return
            }

            val availableDrivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager)
            val devices = availableDrivers.map { driver ->
                UsbSerialDevice(
                    device = driver.device,
                    driver = driver,
                    name = "${driver.device.deviceName} (${driver.javaClass.simpleName})",
                    vendorId = driver.device.vendorId,
                    productId = driver.device.productId
                )
            }
            _availableDevices.value = devices
            Log.d(TAG, "Found ${devices.size} USB serial devices")
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing device list", e)
            _errorMessage.value = "Error scanning for USB devices: ${e.message}"
            _availableDevices.value = emptyList()
        }
    }
    
    fun requestConnection(device: UsbSerialDevice, baudRate: Int = 9600) {
        if (usbManager.hasPermission(device.device)) {
            connectToDevice(device.device, baudRate)
        } else {
            val flags = PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE

            val permissionIntent = PendingIntent.getBroadcast(
                context, 0, Intent(ACTION_USB_PERMISSION), flags
            )
            usbManager.requestPermission(device.device, permissionIntent)
        }
    }
    
    private fun connectToDevice(device: UsbDevice, baudRate: Int = 9600) {
        try {
            _connectionState.value = ConnectionState.CONNECTING
            
            val driver = UsbSerialProber.getDefaultProber().probeDevice(device)
            if (driver == null) {
                _errorMessage.value = "No driver found for device"
                _connectionState.value = ConnectionState.DISCONNECTED
                return
            }
            
            val connection = usbManager.openDevice(driver.device)
            if (connection == null) {
                _errorMessage.value = "Failed to open device connection"
                _connectionState.value = ConnectionState.DISCONNECTED
                return
            }
            
            usbSerialPort = driver.ports[0].apply {
                open(connection)
                setParameters(baudRate, 8, UsbSerialPort.STOPBITS_1, UsbSerialPort.PARITY_NONE)

                // Additional configuration for better compatibility
                try {
                    // Set DTR and RTS for proper handshaking
                    dtr = true
                    rts = true
                    Log.d(TAG, "Set DTR and RTS to true")
                } catch (e: Exception) {
                    Log.w(TAG, "Could not set DTR/RTS: ${e.message}")
                }

                Log.d(TAG, "Serial port configured: $baudRate baud, 8N1, DTR/RTS enabled")
            }
            
            _connectionState.value = ConnectionState.CONNECTED
            _errorMessage.value = null
            startReading()
            
            Log.d(TAG, "Connected to ${device.deviceName} at $baudRate baud")
            
        } catch (e: IOException) {
            Log.e(TAG, "Error connecting to device", e)
            _errorMessage.value = "Connection failed: ${e.message}"
            _connectionState.value = ConnectionState.DISCONNECTED
        }
    }
    
    private fun startReading() {
        readJob?.cancel()
        readJob = CoroutineScope(Dispatchers.IO).launch {
            val buffer = ByteArray(8192)
            Log.d(TAG, "Started reading from serial port...")

            while (isActive && usbSerialPort != null) {
                try {
                    val numBytesRead = usbSerialPort?.read(buffer, READ_WAIT_MILLIS) ?: 0
                    if (numBytesRead > 0) {
                        // Use UTF-8 encoding for proper character handling
                        val data = String(buffer, 0, numBytesRead, Charsets.UTF_8)
                        val hexData = bytesToHex(buffer, numBytesRead)

                        // Update received data on main thread to ensure UI updates
                        withContext(Dispatchers.Main) {
                            _receivedData.value = _receivedData.value + data
                        }

                        // Enhanced logging with both string and hex representation
                        Log.d(TAG, "Received $numBytesRead bytes: '$data'")
                        Log.d(TAG, "Received hex: $hexData")

                        // Log individual characters for debugging
                        for (i in 0 until numBytesRead) {
                            val char = buffer[i].toInt() and 0xFF
                            Log.v(TAG, "Byte $i: 0x${char.toString(16).padStart(2, '0')} ('${if (char >= 32 && char < 127) char.toChar() else '?'}')")
                        }
                    } else if (numBytesRead == 0) {
                        // No data available, this is normal with timeout
                        Log.v(TAG, "Read timeout - no data available")
                    }
                } catch (e: IOException) {
                    if (isActive) {
                        Log.e(TAG, "Error reading from serial port", e)
                        withContext(Dispatchers.Main) {
                            _errorMessage.value = "Read error: ${e.message}"
                        }
                        break
                    }
                } catch (e: Exception) {
                    if (isActive) {
                        Log.e(TAG, "Unexpected error while reading", e)
                        withContext(Dispatchers.Main) {
                            _errorMessage.value = "Unexpected read error: ${e.message}"
                        }
                        break
                    }
                }
            }
            Log.d(TAG, "Stopped reading from serial port")
        }
    }
    
    fun sendData(data: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val bytes = data.toByteArray(Charsets.UTF_8)
                val hexData = bytesToHex(bytes, bytes.size)

                Log.d(TAG, "Sending ${bytes.size} bytes: '$data'")
                Log.d(TAG, "Sending hex: $hexData")

                val bytesWritten = usbSerialPort?.write(bytes, WRITE_WAIT_MILLIS) ?: 0
                Log.d(TAG, "Successfully wrote $bytesWritten bytes")

                // Log individual characters for debugging
                for (i in bytes.indices) {
                    val char = bytes[i].toInt() and 0xFF
                    Log.v(TAG, "Sent byte $i: 0x${char.toString(16).padStart(2, '0')} ('${if (char >= 32 && char < 127) char.toChar() else '?'}')")
                }

            } catch (e: IOException) {
                Log.e(TAG, "Error writing to serial port", e)
                _errorMessage.value = "Write error: ${e.message}"
            } catch (e: Exception) {
                Log.e(TAG, "Unexpected error while writing", e)
                _errorMessage.value = "Unexpected write error: ${e.message}"
            }
        }
    }
    
    fun clearReceivedData() {
        _receivedData.value = ""
    }

    fun clearError() {
        _errorMessage.value = null
    }
    
    fun disconnect() {
        readJob?.cancel()
        try {
            usbSerialPort?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing serial port", e)
        }
        usbSerialPort = null
        _connectionState.value = ConnectionState.DISCONNECTED
        Log.d(TAG, "Disconnected from USB serial device")
    }
    
    fun cleanup() {
        disconnect()
        try {
            context.unregisterReceiver(usbReceiver)
        } catch (e: IllegalArgumentException) {
            // Receiver was not registered
        }
    }
}

enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED
}

data class UsbSerialDevice(
    val device: UsbDevice,
    val driver: UsbSerialDriver,
    val name: String,
    val vendorId: Int,
    val productId: Int
)
