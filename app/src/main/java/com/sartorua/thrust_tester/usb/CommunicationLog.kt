package com.sartorua.thrust_tester.usb

import java.text.SimpleDateFormat
import java.util.*

/**
 * Represents a single communication entry in the log
 */
data class CommunicationEntry(
    val timestamp: Long,
    val direction: Direction,
    val data: String
) {
    enum class Direction {
        TX, // Transmitted (sent)
        RX  // Received
    }
    
    fun getFormattedTimestamp(): String {
        val formatter = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
        return formatter.format(Date(timestamp))
    }
    
    fun getFormattedEntry(): String {
        return "[${getFormattedTimestamp()}] ${direction.name}: $data"
    }
}

/**
 * Manages the communication log with TX and RX entries
 */
class CommunicationLog {
    private val entries = mutableListOf<CommunicationEntry>()
    private val maxEntries = 1000 // Limit to prevent memory issues
    
    /**
     * Add a transmitted (TX) entry to the log
     */
    fun addTxEntry(data: String) {
        addEntry(CommunicationEntry.Direction.TX, data)
    }
    
    /**
     * Add a received (RX) entry to the log
     */
    fun addRxEntry(data: String) {
        addEntry(CommunicationEntry.Direction.RX, data)
    }
    
    private fun addEntry(direction: CommunicationEntry.Direction, data: String) {
        synchronized(entries) {
            entries.add(CommunicationEntry(
                timestamp = System.currentTimeMillis(),
                direction = direction,
                data = data
            ))
            
            // Remove old entries if we exceed the limit
            if (entries.size > maxEntries) {
                entries.removeAt(0)
            }
        }
    }
    
    /**
     * Get all entries as a list
     */
    fun getAllEntries(): List<CommunicationEntry> {
        synchronized(entries) {
            return entries.toList()
        }
    }
    
    /**
     * Get formatted log as a single string
     */
    fun getFormattedLog(): String {
        synchronized(entries) {
            return entries.joinToString("\n") { it.getFormattedEntry() }
        }
    }
    
    /**
     * Clear all entries
     */
    fun clear() {
        synchronized(entries) {
            entries.clear()
        }
    }
    
    /**
     * Get the number of entries
     */
    fun size(): Int {
        synchronized(entries) {
            return entries.size
        }
    }
}
